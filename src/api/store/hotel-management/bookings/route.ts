import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { getOrdersListWorkflow } from "@camped-ai/medusa/core-flows";

// Extended request type to include JWT token data
interface AuthenticatedRequest extends MedusaRequest {
  user?: {
    customer_id?: string;
    actor_id?: string;
    actor_type?: string;
    app_metadata?: {
      customer_id?: string;
    };
  };
}

/**
 * Get customer's bookings
 */
export async function GET(req: AuthenticatedRequest, res: MedusaResponse) {
  try {
    console.log("req.headers:", req.headers);

    // Try to get customer ID from different possible sources
    let customerId = req.session?.customer_id;

    // If not in session directly, try to get from JWT token data
    if (!customerId && req.user) {
      // Check if customer_id is in user object directly
      customerId = req.user.customer_id;

      // Or check if it's in the app_metadata
      if (!customerId && req.user.app_metadata?.customer_id) {
        customerId = req.user.app_metadata.customer_id;
      }

      // Or check if actor_id is a customer ID
      if (
        !customerId &&
        req.user.actor_id &&
        req.user.actor_type === "customer"
      ) {
        customerId = req.user.actor_id;
      }
    }

    // Try to extract from Authorization header (Bearer token)
    if (!customerId && req.headers.authorization) {
      try {
        const authHeader = req.headers.authorization;

        // Check if it's a Bearer token
        if (authHeader.startsWith("Bearer ")) {
          const token = authHeader.substring(7); // Remove 'Bearer ' prefix
          console.log("Found JWT token in Authorization header:", token);

          // If the token is JWT format, try to decode it
          if (token.includes(".")) {
            const parts = token.split(".");
            if (parts.length === 3) {
              try {
                // Decode the payload (middle part)
                const payload = JSON.parse(
                  Buffer.from(parts[1], "base64").toString()
                );
                console.log("Decoded JWT payload:", payload);

                // Extract customer ID from payload
                if (payload.actor_type === "customer" && payload.actor_id) {
                  customerId = payload.actor_id;
                } else if (payload.app_metadata?.customer_id) {
                  customerId = payload.app_metadata.customer_id;
                } else if (payload.customer_id) {
                  customerId = payload.customer_id;
                }
              } catch (e) {
                console.error("Error decoding JWT:", e);
              }
            }
          }
        }
      } catch (e) {
        console.error(
          "Error extracting customer ID from Authorization header:",
          e
        );
      }
    }

    // Try to extract from x-customer-id header directly
    if (!customerId && req.headers["x-customer-id"]) {
      customerId = req.headers["x-customer-id"] as string;
      console.log("Found customer ID in x-customer-id header:", customerId);
    }

    // Fallback: Try to extract from cookies if still no customer ID
    if (!customerId && req.headers.cookie) {
      try {
        // Find the JWT token in the cookies
        const cookies = req.headers.cookie.split(";").map((c) => c.trim());
        const jwtCookie = cookies.find((c) => c.startsWith("connect.sid="));

        if (jwtCookie) {
          const token = jwtCookie.split("=")[1];
          console.log("Found JWT token in cookies:", token);

          // If the token is JWT format, try to decode it
          if (token.includes(".")) {
            const parts = token.split(".");
            if (parts.length === 3) {
              try {
                // Decode the payload (middle part)
                const payload = JSON.parse(
                  Buffer.from(parts[1], "base64").toString()
                );
                console.log("Decoded JWT payload:", payload);

                // Extract customer ID from payload
                if (payload.actor_type === "customer" && payload.actor_id) {
                  customerId = payload.actor_id;
                } else if (payload.app_metadata?.customer_id) {
                  customerId = payload.app_metadata.customer_id;
                }
              } catch (e) {
                console.error("Error decoding JWT:", e);
              }
            }
          }
        }
      } catch (e) {
        console.error("Error extracting customer ID from cookies:", e);
      }
    }

    console.log("Customer ID extracted:", customerId);

    if (!customerId) {
      return res.status(401).json({
        message:
          "Unauthorized. Please provide a valid customer ID via one of the following methods: Authorization header (Bearer token), x-customer-id header, or session cookie.",
        supportedAuthMethods: [
          "Authorization: Bearer <jwt_token>",
          "x-customer-id: <customer_id>",
          "Cookie: connect.sid=<session_cookie>",
        ],
      });
    }

    console.log(`Fetching bookings for customer: ${customerId}`);

    try {
      // Use the workflow to get orders for this customer
      const { result } = await getOrdersListWorkflow(req.scope).run({
        input: {
          fields: [
            "id",
            "status",
            "email",
            "currency_code",
            "payment_status",
            "metadata",
            "created_at",
            "updated_at",
            "total",
          ],
          variables: {
            order: { created_at: "DESC" },
            filters: {
              customer_id: customerId,
            },
          },
        },
      });

      // The result structure might be { rows: [...], metadata: {...} }
      const orders = Array.isArray(result) ? result : result.rows || [];

      console.log(`Found ${orders.length} orders for customer`);

      // Transform orders into bookings
      const bookings = orders
        .filter((order: any) => order.metadata && order.metadata.hotel_id)
        .map((order: any) => {
          const metadata = order.metadata || {};

          return {
            id: order.id,
            order_id: order.id,
            guest_email: order.email,
            guest_name: metadata.guest_name || "Guest",
            guest_phone: metadata.guest_phone,
            hotel_id: metadata.hotel_id,
            room_id: metadata.room_id,
            room_config_id: metadata.room_config_id,
            room_type: metadata.room_type || "Standard",
            check_in_date: metadata.check_in_date
              ? new Date(metadata.check_in_date)
              : null,
            check_out_date: metadata.check_out_date
              ? new Date(metadata.check_out_date)
              : null,
            check_in_time: metadata.check_in_time || "12:00",
            check_out_time: metadata.check_out_time || "12:00",
            number_of_guests: metadata.number_of_guests || 1,
            currency_code: order.currency_code,
            status: order.status,
            payment_status: order.payment_status,
            special_requests: metadata.special_requests,
            notes: metadata.notes,
            total_amount: order.total,
            created_at: order.created_at,
            updated_at: order.updated_at,
          };
        });

      // Get unique hotel IDs from all bookings
      const hotelIds = [
        ...new Set(bookings.map((booking) => booking.hotel_id)),
      ];

      // Get unique room configuration IDs from all bookings
      const roomConfigIds = [
        ...new Set(
          bookings.map((booking) => booking.room_config_id).filter(Boolean)
        ),
      ];

      // Fetch hotel details for all hotel IDs
      const hotelDetails = {};
      if (hotelIds.length > 0) {
        try {
          // Get query from scope
          const query = req.scope.resolve("query");

          const { data: hotels } = await query.graph({
            entity: "hotel",
            filters: {
              id: hotelIds,
            },
            fields: ["id", "name"],
          });

          // Create a map of hotel_id to hotel name
          if (hotels && hotels.length > 0) {
            hotels.forEach((hotel: { id: string; name: string }) => {
              hotelDetails[hotel.id] = hotel.name;
            });
          }
        } catch (error) {
          console.error("Error fetching hotel details:", error);
        }
      }

      // Fetch room configuration details for all room config IDs
      const roomConfigDetails = {};
      if (roomConfigIds.length > 0) {
        try {
          // Get query from scope
          const query = req.scope.resolve("query");

          const { data: products } = await query.graph({
            entity: "product",
            filters: {
              id: roomConfigIds,
            },
            fields: ["id", "title"],
          });

          // Create a map of room_config_id to room config name
          if (products && products.length > 0) {
            products.forEach((product: { id: string; title: string }) => {
              roomConfigDetails[product.id] = product.title;
            });
          }
        } catch (error) {
          console.error("Error fetching room configuration details:", error);
        }
      }

      // Add hotel names and room configuration names to bookings
      const bookingsWithDetails = bookings.map((booking) => ({
        ...booking,
        hotel_name: hotelDetails[booking.hotel_id] || "Unknown Hotel",
        room_config_name:
          roomConfigDetails[booking.room_config_id] || "Unknown Room Type",
      }));

      res.json({
        bookings: bookingsWithDetails,
        count: bookingsWithDetails.length,
      });
    } catch (error) {
      console.error("Error fetching bookings:", error);
      return res.status(400).json({
        message: "Failed to fetch bookings",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  } catch (error) {
    console.error("Error in GET bookings:", error);
    res.status(400).json({
      message:
        error instanceof Error ? error.message : "Failed to get bookings",
    });
  }
}
